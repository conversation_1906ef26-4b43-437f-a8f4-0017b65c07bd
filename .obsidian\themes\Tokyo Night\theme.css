/*Fonts*/
:root {
  --default-font: '"JetBrains Mono", monospace, "Inter", sans-serif';
  --font-monospace: '"JetBrains Mono", monospace, "Source Code Pro", monospace';
}

.theme-dark {
  color-scheme: dark;
  /* --accent-h: 261; /* magenta */
  /* --accent-s: 85%; */
  /* --accent-l: 79%; */
  /* cyan */
  --accent-h: 202;
  --accent-s: 100%;
  --accent-l: 75%;

  --bg_dark2_x: 18, 18, 24;
  --bg_dark2: rgb(var(--bg_dark2_x));
  --bg_dark_x: 22, 22, 30;
  --bg_dark: rgb(var(--bg_dark_x));
  --bg_x: 26, 27, 38;
  --bg: rgb(var(--bg_x));
  --bg_highlight_x: 41, 46, 66;
  --bg_highlight: rgb(var(--bg_highlight_x));
  --bg_highlight_dark_x: 36, 40, 59;
  --bg_highlight_dark: rgb(var(--bg_highlight_dark_x));
  --terminal_black_x: 65, 72, 104;
  --terminal_black: rgb(var(--terminal_black_x));
  --fg_x: 192, 202, 245;
  --fg: rgb(var(--fg_x));
  --fg_dark_x: 169, 177, 214;
  --fg_dark: rgb(var(--fg_dark_x));
  --comment_x: 86, 95, 137;
  --comment: rgb(var(--comment_x));
  --blue0_x: 61, 89, 161;
  --blue0: rgb(var(--blue0_x));
  --blue_x: 122, 162, 247;
  --blue: rgb(var(--blue_x));
  --cyan_hsl: 202 100% 75%;
  --cyan_x: 125, 207, 255;
  --cyan: rgb(var(--cyan_x));
  --magent_hsl: 261 85% 79%;
  --magenta_x: 187, 154, 247;
  --magenta: rgb(var(--magenta_x));
  --pink_hsl: 331 100% 43%;
  --pink_x: 217, 0, 105;
  --pink: rgb(var(--pink_x));
  --orange_x: 255, 158, 100;
  --orange: rgb(var(--orange_x));
  --yellow_x: 224, 175, 104;
  --yellow: rgb(var(--yellow_x));
  --green_x: 158, 206, 106;
  --green: rgb(var(--green_x));
  --teal_x: 26, 188, 156;
  --teal: rgb(var(--teal_x));
  --red_x: 255, 117, 127;
  --red: rgb(var(--red_x));
  --red1_x: 219, 75, 75;
  --red1: rgb(var(--red1_x));
  --unknown: #ffffff;

  --link-external-filter: invert(35%) sepia(28%) saturate(681%) hue-rotate(192deg) brightness(94%) contrast(85%);
}

.theme-light {
  /* --accent-h: 261; /* magenta */
  /* --accent-s: 24%; */
  /* --accent-l: 38%; */
  /* cyan */
  --accent-h: 202;
  --accent-s: 86%;
  --accent-l: 43%;

  --bg_dark2_x: 188, 189, 194;
  --bg_dark2: rgb(var(--bg_dark2_x));
  --bg_dark_x: 203, 204, 209;
  --bg_dark: rgb(var(--bg_dark_x));
  --bg_x: 213, 214, 219;
  --bg: rgb(var(--bg_x));
  --bg_highlight_x: 220, 222, 226;
  --bg_highlight: rgb(var(--bg_highlight_x));
  --bg_highlight_dark_x: 195, 197, 201;
  --bg_highlight_dark: rgb(var(--bg_highlight_dark_x));
  --terminal_black_x: 15, 15, 20;
  --terminal_black: rgb(var(--terminal_black_x));
  --fg_x: 52, 59, 88;
  --fg: rgb(var(--fg_x));
  --fg_dark_x: 39, 46, 75;
  --fg_dark: rgb(var(--fg_dark_x));
  --comment_x: 150, 153, 163;
  --comment: rgb(var(--comment_x));
  --blue0_x: 39, 71, 125;
  --blue0: rgb(var(--blue0_x));
  --blue_x: 52, 84, 138;
  --blue: rgb(var(--blue_x));
  --cyan_x: 15, 75, 110;
  --cyan: rgb(var(--cyan_x));
  --magent_hsl: 261 24% 38%;
  --magenta_x: 90, 74, 120;
  --magenta: rgb(var(--magenta_x));
  --pink_hsl: 286 24% 38%;
  --pink_x: 109, 74, 120;
  --pink: rgb(var(--pink_x));
  --orange_x: 150, 80, 39;
  --orange: rgb(var(--orange_x));
  --yellow_x: 143, 94, 21;
  --yellow: rgb(var(--yellow_x));
  --green_x: 51, 99, 92;
  --green: rgb(var(--green_x));
  --teal_x: 22, 103, 117;
  --teal: rgb(var(--teal_x));
  --red_x: 140, 67, 81;
  --red: rgb(var(--red_x));
  --red1_x: 115, 42, 56;
  --red1: rgb(var(--red1_x));
  --unknown: #000000;

  --link-external-filter: invert(62%) sepia(9%) saturate(266%) hue-rotate(189deg) brightness(97%) contrast(88%);
}

.theme-dark,
.theme-light {
  --color_red_rgb: var(--red_x);
  --color-red: var(--red);
  --color_purple_rgb: var(--magenta_x);
  --color-purple: var(--magenta);
  --color_green_rgb: var(--green_x);
  --color-green: var(--green);
  --color_cyan_rgb: var(--cyan_x);
  --color-cyan: var(--cyan);
  --color_blue_rgb: var(--blue_x);
  --color-blue: var(--blue);
  --color_yellow_rgb: var(--yellow_x);
  --color-yellow: var(--yellow);
  --color_orange_rgb: var(--orange_x);
  --color-orange: var(--orange);
  --color_pink_rgb: var(--magenta_x);
  --color-pink: var(--magenta);

  --background-primary: var(--bg);
  --background-primary-alt: var(--bg);
  --background-secondary: var(--bg_dark);
  --background-secondary-alt: var(--bg_dark);

  --background-modifier-border: var(--bg_highlight);
  --background-modifier-border-focus: var(--bg_highlight);
  --background-modifier-border-hover: var(--bg_highlight);
  --background-modifier-form-field: var(--bg_dark);
  --background-modifier-form-field-highlighted: var(--bg_dark);
  --background-modifier-box-shadow: rgba(0, 0, 0, 0.3);
  --background-modifier-success: var(--green);
  --background-modifier-error: var(--red1);
  --background-modifier-error-hover: var(--red);
  --background-modifier-cover: rgba(var(--bg_dark_x), 0.8);
  --background-modifier-hover: var(--bg_highlight);
  --background-modifier-message: rgba(var(--bg_highlight_x), 0.9);
  --background-modifier-active-hover: var(--bg_highlight);

  --text-normal: var(--fg);
  --text-faint: var(--comment);
  --text-muted: var(--fg_dark);
  --text-error: var(--red);
  --text-accent: var(--magenta);
  --text-accent-hover: var(--cyan);
  --text-error: var(--red1);
  --text-error-hover: var(--red);
  --text-selection: var(--unknown);
  --text-on-accent: var(--bg);
  --text-highlight-bg: rgba(var(--orange_x), 0.25);
  --text-selection: rgba(var(--blue0_x), 0.6);
  --bold-color: var(--cyan);
  --italic-color: var(--cyan);

  --interactive-normal: var(--bg_dark);
  --interactive-hover: var(--bg);
  --interactive-success: var(--green);
  --interactive-accent: hsl(var(--accent-h), var(--accent-s), var(--accent-l));
  --interactive-accent-hover: var(--blue);

  --scrollbar-bg: var(--bg_dark2);
  --scrollbar-thumb-bg: var(--comment);
  --scrollbar-active-thumb-bg: var(--bg_dark);
  --scrollbar-width: 0px;

  --h1-color: var(--red);
  --h2-color: var(--yellow);
  --h3-color: var(--green);
  --h4-color: var(--cyan);
  --h5-color: var(--blue);
  --h6-color: var(--magenta);

  --border-width: 2px;

  --tag-color: var(--magenta);
  --tag-background: rgba(var(--magenta_x), 0.15);
  --tag-color-hover: var(--cyan);
  --tag-background-hover: rgba(var(--cyan_x), 0.15);

  --link-color: var(--magenta);
  --link-color-hover: var(--cyan);
  --link-external-color: var(--magenta);
  --link-external-color-hover: var(--cyan);

  --checkbox-radius: var(--radius-l);
  --checkbox-color: var(--green);
  --checkbox-color-hover: var(--green);
  --checkbox-marker-color: var(--bg);
  --checkbox-border-color: var(--comment);
  --checkbox-border-color-hover: var(--comment);

  --table-header-background: var(--bg_dark2);
  --table-header-background-hover: var(--bg_dark2);

  --flashing-background: rgba(var(--blue0_x), 0.3);

  --code-normal: var(--fg);
  --code-background: var(--bg_highlight_dark);

  --mermaid-note: var(--blue0);
  --mermaid-actor: var(--fg_dark);
  --mermaid-loopline: var(--blue);

  --blockquote-background-color: var(--bg_dark);
  --callout-default: var(--blue_x);
  --callout-info: var(--blue_x);
  --callout-summary: var(--cyan_x);
  --callout-tip: var(--cyan_x);
  --callout-todo: var(--cyan_x);
  --callout-bug: var(--red_x);
  --callout-error: var(--red1_x);
  --callout-fail: var(--red1_x);
  --callout-example: var(--magenta_x);
  --callout-important: var(--green_x);
  --callout-success: var(--teal_x);
  --callout-question: var(--yellow_x);
  --callout-warning: var(--orange_x);
  --callout-quote: var(--fg_dark_x);

  --icon-color-hover: var(--blue);
  --icon-color-focused: var(--magenta);
  --icon-color-active: var(--magenta);

  --nav-item-color-hover: var(--fg);
  --nav-item-background-hover: var(--bg_highlight);
  --nav-item-color-active: var(--red);
  --nav-item-background-active: var(--bg_highlight);
  --nav-file-tag: rgba(var(--yellow_x), 0.9);
  --nav-indentation-guide-color: var(--bg_highlight);

  --indentation-guide-color: var(--comment);
  --indentation-guide-color-active: var(--comment);

  --graph-line: var(--comment);
  --graph-node: var(--fg);
  --graph-node-tag: var(--orange);
  --graph-node-attachment: var(--blue);

  --tab-text-color-focused-active: rgba(var(--red_x), 0.8);
  --tab-text-color-focused-active-current: var(--red);

  --modal-border-color: var(--bg_highlight);
  --prompt-border-color: var(--bg_highlight);
  --slider-track-background: var(--bg_highlight);

  --embed-background: var(--bg_dark);
  --embed-padding: 1.5rem 1.5rem 0.5rem;

  --canvas-color: var(--bg_highlight_x);

  --toggle-thumb-color: var(--bg);

  --image-radius: 0px;

  --date-color: var(--blue);
  --date-background-color: var(--bg_highlight);
}


/* Headings (credit: https://github.com/Akifyss/obsidian-border)*/

body:not(.heading-indicator-off) .markdown-rendered>:is(h1, h2, h3, h4, h5, h6),
body:not(.heading-indicator-off) .markdown-preview-sizer>div>:is(h1, h2, h3, h4, h5, h6) {
  position: relative;
}

body.h1-divider-on :is(.markdown-preview-sizer>div>h1, .markdown-rendered>h1, .HyperMD-header-1)::after {
  content: "";
  position: absolute;
  height: 2px;
  width: 100%;
  right: 0px;
  opacity: 0.5;
  bottom: 0;
  background-image: linear-gradient(to left, var(--h1-color) 30%, transparent 70%);
}

body.h2-divider-on :is(.markdown-preview-sizer>div>h2, .markdown-rendered>h2, .HyperMD-header-2)::after {
  content: "";
  position: absolute;
  height: 2px;
  width: 100%;
  right: 0px;
  opacity: 0.5;
  bottom: 0;
  background-image: linear-gradient(to left, var(--h2-color) 30%, transparent 70%);
}

body.h3-divider-on :is(.markdown-preview-sizer>div>h3, .markdown-rendered>h3, .HyperMD-header-3)::after {
  content: "";
  position: absolute;
  height: 2px;
  width: 100%;
  right: 0px;
  opacity: 0.5;
  bottom: 0;
  background-image: linear-gradient(to left, var(--h3-color) 30%, transparent 70%);
}

body.h4-divider-on :is(.markdown-preview-sizer>div>h4, .markdown-rendered>h4, .HyperMD-header-4)::after {
  content: "";
  position: absolute;
  height: 2px;
  width: 100%;
  right: 0px;
  opacity: 0.5;
  bottom: 0;
  background-image: linear-gradient(to left, var(--h4-color) 30%, transparent 70%);
}

body.h5-divider-on :is(.markdown-preview-sizer>div>h5, .markdown-rendered>h5, .HyperMD-header-5)::after {
  content: "";
  position: absolute;
  height: 2px;
  width: 100%;
  right: 0px;
  opacity: 0.5;
  bottom: 0;
  background-image: linear-gradient(to left, var(--h5-color) 30%, transparent 70%);
}

body.h6-divider-on :is(.markdown-preview-sizer>div>h6, .markdown-rendered>h6, .HyperMD-header-6)::after {
  content: "";
  position: absolute;
  height: 2px;
  width: 100%;
  right: 0px;
  opacity: 0.5;
  bottom: 0;
  background-image: linear-gradient(to left, var(--h6-color) 30%, transparent 70%);
}

/* mobile */
.is-mobile .suggestion-item.is-selected {
  background-color: var(--bg_highlight);
}

/* scrollbars */
::-webkit-scrollbar {
  width: var(--scrollbar-width);
}

::-webkit-scrollbar:horizontal {
  height: var(--scrollbar-width);
}

/* tooltip */
.tooltip {
  color: var(--fg);
}

/* links */

.cm-url {
  filter: none !important;
}

.cm-string.cm-url.external-link {
  filter: var(--link-external-filter) !important;
}

a.external-link {
  filter: none;
  background-image: unset;
  padding-right: 0;
}

a.external-link::after {
  content: url(public/images/874d8b8e340f75575caa.svg);
  filter: var(--link-external-filter);
  padding-left: 2.5px;
  bottom: -1px;
  position: relative;
}

/* Images  */

.image-embed {
  border-radius: var(--image-radius);
}

/* tables credit: Akifyss/border */
body {
  --table-width: 88cqw;
}

.theme-dark,
.theme-light .markdown-rendered thead tr {
  background: var(--table-header-background);
}

body:not(.table-width-obsidian-default) .markdown-source-view.mod-cm6 .cm-table-widget .table-wrapper,
body:not(.table-width-obsidian-default) :is(.markdown-rendered, .markdown-source-view.mod-cm6.is-live-preview) table {
  width: 100% !important;
}

.table-width-customized .markdown-source-view.mod-cm6.is-live-preview .cm-content>.cm-table-widget,
.table-width-customized .markdown-rendered .markdown-preview-sizer>div:has(>table) {
  overflow: auto;
  width: var(--table-width) !important;
  margin-left: calc((var(--table-width) - 100%) / -2) !important;
}

/* notice */
.notice {
  background-color: var(--bg_dark);
  border: 2px solid var(--bg_highlight);
}

/* callouts */
.callout[data-callout="quote"] {
  background-color: rgba(var(--bg_highlight_dark_x), 0.5);
}


.callout[data-callout="cite"] {
  --callout-color: var(--pink_x);
  background-color: rgba(var(--pink_x), 0.15);
}

/* the following callouts are from Border (https://github.com/Akifyss/obsidian-border/blob/18607ee208d56e1d4faff7419efb9cf7c9cc726e/theme.css#L7710C1-L7799C2) */

.callout-style-1.theme-light {
  --callout-border-width: 1px;
  --callout-border-opacity: 0.25;
  --callout-padding: 0;
  --callout-radius: var(--radius-s);
  --callout-title-color: inherit;
  --callout-title-padding: 8px 16px;
  --callout-title-size: inherit;
  --callout-content-padding: 0px 16px;
  --callout-content-background: #FFFFFFBF;
  --callout-content-radius: 0px;
}

.callout-style-1.theme-dark {
  --callout-border-width: 1px;
  --callout-border-opacity: 0.25;
  --callout-padding: 0;
  --callout-radius: var(--radius-s);
  --callout-title-color: inherit;
  --callout-title-padding: 8px 16px;
  --callout-title-size: inherit;
  --callout-content-padding: 0px 16px;
  --callout-content-background: #00000040;
  --callout-content-radius: 0px;
}

.callout-style-2.theme-light {
  --callout-border-width: 1px;
  --callout-border-opacity: 0.25;
  --callout-padding: 0 6px 6px;
  --callout-radius: var(--radius-s);
  --callout-title-color: inherit;
  --callout-title-padding: 8px 16px;
  --callout-title-size: inherit;
  --callout-content-padding: 0px 16px;
  --callout-content-background: #FFFFFFBF;
  --callout-content-radius: 4px;
}

.callout-style-2.theme-dark {
  --callout-border-width: 1px;
  --callout-border-opacity: 0.25;
  --callout-padding: 0 6px 6px;
  --callout-radius: var(--radius-s);
  --callout-title-color: inherit;
  --callout-title-padding: 8px 16px;
  --callout-title-size: inherit;
  --callout-content-padding: 0px 16px;
  --callout-content-background: #00000040;
  --callout-content-radius: 4px;
}

.callout-style-3:is(.theme-light, .theme-dark) {
  --callout-border-width: 0 0 0 4px;
  --callout-border-opacity: 1;
  --callout-padding: var(--size-4-3) var(--size-4-3) var(--size-4-3) var(--size-4-6);
  --callout-radius: var(--radius-s);
  --callout-title-color: inherit;
  --callout-title-padding: 0;
  --callout-title-size: inherit;
  --callout-content-padding: 0;
  --callout-content-background: transparent;
  --callout-content-radius: 0px;
}

.callout-style-4.theme-light {
  --callout-border-width: 0 0 0 4px;
  --callout-border-opacity: 1;
  --callout-padding: 0;
  --callout-radius: var(--radius-s);
  --callout-title-color: inherit;
  --callout-title-padding: 8px 16px;
  --callout-title-size: inherit;
  --callout-content-padding: 0px 16px;
  --callout-content-background: #FFFFFFBF;
  --callout-content-radius: 0px;
}

.callout-style-4.theme-dark {
  --callout-border-width: 0 0 0 4px;
  --callout-border-opacity: 1;
  --callout-padding: 0;
  --callout-radius: var(--radius-s);
  --callout-title-color: inherit;
  --callout-title-padding: 8px 16px;
  --callout-title-size: inherit;
  --callout-content-padding: 0px 16px;
  --callout-content-background: #00000040;
  --callout-content-radius: 0px;
}

/* for vim */
.ͼ2 .cm-panels-bottom {
  border-top: var(--border-width) solid var(--bg_highlight);
}

.cm-vim-message {
  color: var(--red);
}

/* kanban */
.kanban-plugin__lane-action-add {
  color: var(--fg);
}

.kanban-plugin__lane-action-add:hover {
  color: var(--bg);
}

.kanban-plugin__autocomplete-item:hover,
.kanban-plugin__autocomplete-item-active {
  color: hsl(var(--accent-h), var(--accent-s), var(--accent-l));
  background-color: var(--bg_highlight);
}

/* various-complement */
.various-complements__suggestion-item::before {
  filter: var(--link-external-filter);
}

.various-complements__footer::before {
  filter: var(--link-external-filter);
}

/* Math Jax */
mjx-math {
  font-size: 110% !important;
}

/* metadata */
.cm-atom {
  color: var(--blue);
}

span.cm-hmd-frontmatter.cm-meta {
  color: rgba(var(--blue0_x), 0.9);
}

/* checkboxes (credit: border) */

body:not(.disable-alternative-checkboxes) input[data-task="<"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="<"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="<"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task=">"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task=">"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task=">"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="d"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="d"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="d"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="u"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="u"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="u"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="S"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="S"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="S"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="“"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="“"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="“"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="\""]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="\""]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="\""]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="c"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="c"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="c"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="p"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="p"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="p"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="n"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="n"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="n"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="b"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="b"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="b"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="-"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="-"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="-"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="l"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="l"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="l"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="I"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="I"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="I"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="/"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="/"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="/"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="i"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="i"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="i"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="*"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="*"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="*"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="?"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="?"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="?"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="!"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="!"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="!"]>p>input:checked {
  --checkbox-marker-color: transparent;
  border: none;
  border-radius: 0;
  background-image: none;
  background-color: currentColor;
  -webkit-mask-size: var(--checkbox-icon);
  -webkit-mask-position: 50% 50%
}


body:not(.disable-alternative-checkboxes) input[data-task="!"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="!"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="!"]>p>input:checked {
  --checkbox-color-hover: var(--color-orange);
  color: var(--color-orange);
  -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><defs><style>.cls-1{fill:none;}.cls-2{fill-rule:evenodd;}</style></defs><rect class="cls-1" width="16" height="16"/><path class="cls-2" d="M8,16c4.42,0,8-3.58,8-8S12.42,0,8,0,0,3.58,0,8s3.58,8,8,8ZM6.07,3.55c-.08,.29-.09,.6-.03,.9l.86,4.56c.05,.28,.2,.52,.42,.7,.22,.18,.49,.28,.77,.28s.55-.1,.77-.28c.22-.18,.36-.43,.42-.7l.86-4.56c.06-.3,.04-.61-.03-.9-.08-.29-.22-.57-.41-.8-.19-.23-.44-.42-.71-.55-.28-.13-.58-.2-.88-.2s-.61,.07-.88,.2c-.28,.13-.52,.32-.71,.55-.19,.23-.34,.51-.41,.8Zm3.07,10.01c.28-.28,.44-.66,.44-1.06s-.16-.78-.44-1.06c-.28-.28-.66-.44-1.06-.44s-.78,.16-1.06,.44c-.28,.28-.44,.66-.44,1.06s.16,.78,.44,1.06c.28,.28,.66,.44,1.06,.44s.78-.16,1.06-.44Z"/></svg>');
}

body:not(.disable-alternative-checkboxes) input[data-task="?"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="?"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="?"]>p>input:checked {
  --checkbox-color-hover: var(--color-pink);
  color: var(--color-pink);
  -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><defs><style>.cls-1{fill:none;}.cls-2{fill-rule:evenodd;}</style></defs><rect class="cls-1" width="16" height="16"/><path class="cls-2" d="M8,16c4.42,0,8-3.58,8-8S12.42,0,8,0,0,3.58,0,8s3.58,8,8,8ZM4.84,3.66c-.32,.48-.09,1.14,.44,1.37,.23,.1,.49,.1,.72,0,.17-.07,.31-.18,.41-.34,.31-.49,.82-.89,1.6-.89,1.72,0,2.06,1.61,1.45,2.47-.36,.51-.87,.91-1.33,1.37-.28,.28-.55,.58-.74,.93-.19,.33-.27,.69-.31,1.03-.04,.31,.08,.59,.28,.79,.18,.18,.43,.29,.71,.29,.49,0,.89-.38,.95-.87,.04-.3,.1-.45,.2-.63,.41-.76,1.18-1.12,1.98-2.3,.72-1.07,.45-2.5-.02-3.27-.55-.89-1.62-1.61-3.19-1.61-1.43,0-2.51,.7-3.15,1.66Zm3.16,11.01c.73,0,1.33-.6,1.33-1.33s-.6-1.33-1.33-1.33-1.33,.6-1.33,1.33,.6,1.33,1.33,1.33Z"/></svg>');
}

body:not(.disable-alternative-checkboxes) input[data-task="*"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="*"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="*"]>p>input:checked {
  --checkbox-color-hover: var(--color-yellow);
  color: var(--color-yellow);
  -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><defs><style>.cls-1{fill:none;}</style></defs><rect class="cls-1" width="16" height="16"/><path d="M8.91,.58c-.08-.17-.21-.32-.37-.42C8.38,.05,8.19,0,8,0s-.38,.05-.54,.16c-.16,.1-.29,.25-.37,.42l-1.93,4.12L.85,5.36c-.18,.03-.35,.1-.49,.22-.14,.12-.25,.27-.3,.45-.06,.17-.07,.36-.03,.54,.04,.18,.13,.34,.26,.48l3.15,3.23-.75,4.57c-.03,.19,0,.38,.06,.55,.07,.17,.19,.32,.35,.43,.15,.11,.33,.17,.52,.18,.19,0,.37-.03,.54-.12l3.84-2.13,3.84,2.13c.16,.09,.35,.13,.54,.12,.19-.01,.37-.07,.52-.18,.15-.11,.27-.26,.35-.43,.07-.17,.09-.36,.06-.55l-.75-4.57,3.15-3.23c.13-.13,.22-.3,.26-.48,.04-.18,.03-.37-.03-.54-.06-.17-.16-.33-.31-.45-.14-.12-.31-.2-.49-.22l-4.31-.66L8.91,.58Z"/></svg>');
}

body:not(.disable-alternative-checkboxes) input[data-task="i"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="i"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="i"]>p>input:checked {
  --checkbox-color-hover: var(--color-cyan);
  color: var(--color-cyan);
  -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><defs><style>.cls-1{fill:none;}</style></defs><rect class="cls-1" width="16" height="16"/><g><rect class="cls-1" width="16" height="16"/><path d="M10.67,13v-1.67c0-.1-.03-.18-.09-.24s-.14-.09-.24-.09h-1V5.67c0-.1-.03-.18-.09-.24s-.14-.09-.24-.09h-3.33c-.1,0-.18,.03-.24,.09s-.09,.14-.09,.24v1.67c0,.1,.03,.18,.09,.24s.14,.09,.24,.09h1v3.33h-1c-.1,0-.18,.03-.24,.09s-.09,.14-.09,.24v1.67c0,.1,.03,.18,.09,.24s.14,.09,.24,.09h4.67c.1,0,.18-.03,.24-.09s.09-.14,.09-.24Zm-1.33-9.33v-1.67c0-.1-.03-.18-.09-.24s-.14-.09-.24-.09h-2c-.1,0-.18,.03-.24,.09s-.09,.14-.09,.24v1.67c0,.1,.03,.18,.09,.24s.14,.09,.24,.09h2c.1,0,.18-.03,.24-.09s.09-.14,.09-.24Zm6.67,4.33c0,1.45-.36,2.79-1.07,4.02-.72,1.23-1.69,2.2-2.91,2.91-1.23,.72-2.56,1.07-4.02,1.07s-2.79-.36-4.02-1.07c-1.23-.72-2.2-1.69-2.91-2.91-.72-1.23-1.07-2.56-1.07-4.02S.36,5.21,1.07,3.98c.72-1.23,1.69-2.2,2.91-2.91,1.23-.72,2.56-1.07,4.02-1.07s2.79,.36,4.02,1.07c1.23,.72,2.2,1.69,2.91,2.91,.72,1.23,1.07,2.56,1.07,4.02Z"/></g></svg>');
}

body:not(.disable-alternative-checkboxes) input[data-task="/"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="/"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="/"]>p>input:checked {
  --checkbox-color-hover: var(--color-yellow);
  color: var(--color-yellow);
  -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><defs><style>.cls-1{fill:none;}.cls-2{fill-rule:evenodd;}</style></defs><rect class="cls-1" width="16" height="16"/><path class="cls-2" d="M13.66,13.66c-1.45,1.45-3.45,2.34-5.66,2.34C3.58,16,0,12.42,0,8S3.58,0,8,0c2.17,0,4.15,.87,5.59,2.28,.06,.06,.12,.12,.18,.18,1.37,1.43,2.22,3.36,2.23,5.5,0,.03,0,.07,0,.1-.01,2.19-.91,4.17-2.34,5.6Zm-1.41-1.41c-1.09,1.09-2.59,1.76-4.24,1.76-3.31,0-6-2.69-6-6S4.69,2,8,2v6l4.24,4.24Z"/></svg>');
}

body:not(.disable-alternative-checkboxes) input[data-task="I"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="I"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="I"]>p>input:checked {
  --checkbox-color-hover: var(--color-orange);
  color: var(--color-orange);
  -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><defs><style>.cls-1{fill:none;}</style></defs><rect class="cls-1" width="16" height="16"/><g><rect class="cls-1" width="16" height="16"/><path d="M9,1c0-.27-.11-.52-.29-.71-.19-.19-.44-.29-.71-.29s-.52,.11-.71,.29c-.19,.19-.29,.44-.29,.71v1c0,.27,.11,.52,.29,.71,.19,.19,.44,.29,.71,.29s.52-.11,.71-.29c.19-.19,.29-.44,.29-.71V1Zm4.66,2.76c.18-.19,.28-.44,.28-.7,0-.26-.11-.51-.29-.7s-.44-.29-.7-.29c-.26,0-.51,.1-.7,.28l-.71,.71c-.18,.19-.28,.44-.28,.7,0,.26,.11,.51,.29,.7s.44,.29,.7,.29c.26,0,.51-.1,.7-.28l.71-.71Zm2.34,4.24c0,.27-.11,.52-.29,.71-.19,.19-.44,.29-.71,.29h-1c-.27,0-.52-.11-.71-.29-.19-.19-.29-.44-.29-.71s.11-.52,.29-.71c.19-.19,.44-.29,.71-.29h1c.27,0,.52,.11,.71,.29,.19,.19,.29,.44,.29,.71ZM3.05,4.46c.09,.1,.2,.17,.32,.22,.12,.05,.25,.08,.39,.08,.13,0,.26-.02,.39-.07,.12-.05,.23-.12,.33-.22,.09-.09,.17-.21,.22-.33,.05-.12,.08-.25,.07-.39,0-.13-.03-.26-.08-.39-.05-.12-.13-.23-.22-.32l-.71-.71c-.19-.18-.44-.28-.7-.28-.26,0-.51,.11-.7,.29s-.29,.44-.29,.7c0,.26,.1,.51,.28,.7l.71,.71Zm-.05,3.54c0,.27-.11,.52-.29,.71-.19,.19-.44,.29-.71,.29H1c-.27,0-.52-.11-.71-.29-.19-.19-.29-.44-.29-.71s.11-.52,.29-.71c.19-.19,.44-.29,.71-.29h1c.27,0,.52,.11,.71,.29,.19,.19,.29,.44,.29,.71Zm3,6v-1h4v1c0,.53-.21,1.04-.59,1.41-.38,.38-.88,.59-1.41,.59s-1.04-.21-1.41-.59c-.38-.38-.59-.88-.59-1.41Zm4-2c.02-.34,.21-.65,.48-.86,.65-.51,1.13-1.22,1.36-2.02,.23-.8,.21-1.65-.06-2.43-.27-.79-.78-1.47-1.46-1.95-.68-.48-1.49-.74-2.32-.74s-1.64,.26-2.32,.74c-.68,.48-1.19,1.16-1.46,1.95-.27,.79-.29,1.64-.06,2.43,.23,.8,.71,1.5,1.36,2.02,.27,.21,.46,.52,.48,.86h4Z"/></g></svg>');
}

body:not(.disable-alternative-checkboxes) input[data-task="l"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="l"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="l"]>p>input:checked {
  --checkbox-color-hover: var(--color-red);
  color: var(--color-red);
  -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><defs><style>.cls-1{fill:none;}</style></defs><rect class="cls-1" width="16" height="16"/><path d="M8,.12c-1.64,0-3.21,.65-4.37,1.81-1.16,1.16-1.81,2.73-1.81,4.37,0,1.34,.44,2.64,1.25,3.71,0,0,.17,.22,.2,.25l4.74,5.6,4.75-5.6s.19-.25,.19-.25h0c.81-1.07,1.25-2.37,1.25-3.71,0-1.64-.65-3.21-1.81-4.37C11.21,.78,9.64,.13,8,.12Zm0,8.44c-.45,0-.88-.13-1.25-.38-.37-.25-.66-.6-.83-1.01-.17-.41-.21-.86-.13-1.3,.09-.44,.3-.84,.62-1.15,.31-.31,.72-.53,1.15-.62,.44-.09,.89-.04,1.3,.13,.41,.17,.76,.46,1.01,.83,.25,.37,.38,.81,.38,1.25,0,.6-.24,1.17-.66,1.59-.42,.42-.99,.66-1.59,.66Z"/></svg>');
}

body:not(.disable-alternative-checkboxes) input[data-task="-"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="-"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="-"]>p>input:checked {
  --checkbox-color-hover: var(--text-faint);
  color: var(--text-faint);
  -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><defs><style>.cls-1{fill:none;}.cls-2{fill-rule:evenodd;}</style></defs><rect class="cls-1" width="16" height="16"/><g><rect class="cls-1" width="16" height="16"/><path class="cls-2" d="M8,16c4.42,0,8-3.58,8-8S12.42,0,8,0,0,3.58,0,8s3.58,8,8,8ZM3.5,7c-.55,0-1,.45-1,1s.45,1,1,1H12.5c.55,0,1-.45,1-1s-.45-1-1-1H3.5Z"/></g></svg>');
}

body:not(.disable-alternative-checkboxes) body:not(.tasks) .markdown-preview-view ul li[data-task="x"].task-list-item.is-checked,
body:not(.disable-alternative-checkboxes) body:not(.tasks) .markdown-source-view.mod-cm6 .HyperMD-task-line[data-task]:is([data-task="x"]),
body:not(.disable-alternative-checkboxes) body:not(.tasks) li[data-task="x"].task-list-item.is-checked,
body:not(.disable-alternative-checkboxes) body:not(.tasks) .markdown-preview-view ul li[data-task="-"].task-list-item.is-checked,
body:not(.disable-alternative-checkboxes) body:not(.tasks) .markdown-source-view.mod-cm6 .HyperMD-task-line[data-task]:is([data-task="-"]),
body:not(.disable-alternative-checkboxes) body:not(.tasks) li[data-task="-"].task-list-item.is-checked {
  color: var(--text-faint);
  text-decoration: line-through solid var(--text-faint) 1px
}


body:not(.disable-alternative-checkboxes) input[data-task="b"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="b"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="b"]>p>input:checked {
  --checkbox-color-hover: var(--color-blue);
  color: var(--color-blue);
  -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><defs><style>.cls-1{fill:none;}.cls-2{fill-rule:evenodd;}</style></defs><rect class="cls-1" width="16" height="16"/><path class="cls-2" d="M4.25,.5c-.6,0-1.17,.24-1.59,.66-.42,.42-.66,.99-.66,1.59V14.1c0,.2,.05,.39,.15,.56,.1,.17,.24,.31,.41,.41,.17,.1,.36,.15,.56,.15,.2,0,.39-.05,.56-.15l3.94-2.25c.11-.06,.24-.1,.37-.1s.26,.03,.37,.1l3.95,2.25c.17,.1,.36,.15,.56,.15,.2,0,.39-.05,.56-.15,.17-.1,.31-.24,.41-.41s.15-.36,.15-.56V2.75c0-.6-.24-1.17-.66-1.59-.42-.42-.99-.66-1.59-.66H4.25Z"/></svg>');
}

body:not(.disable-alternative-checkboxes) input[data-task="n"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="n"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="n"]>p>input:checked {
  --checkbox-color-hover: var(--color-cyan);
  color: var(--color-cyan);
  -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><defs><style>.cls-1{fill:none;}</style></defs><rect class="cls-1" width="16" height="16"/><path d="M2.75,14.75c-.41,0-.77-.15-1.06-.44-.29-.29-.44-.65-.44-1.06V2.75c0-.41,.15-.77,.44-1.06,.29-.29,.65-.44,1.06-.44H13.25c.41,0,.77,.15,1.06,.44,.29,.29,.44,.65,.44,1.06v7.5l-4.5,4.5H2.75Zm4.5-5.25c.21,0,.39-.07,.53-.22,.14-.14,.22-.32,.22-.53s-.07-.39-.22-.53c-.14-.14-.32-.22-.53-.22h-2.25c-.21,0-.39,.07-.53,.22-.14,.14-.22,.32-.22,.53s.07,.39,.22,.53c.14,.14,.32,.22,.53,.22h2.25Zm3.75-3c.21,0,.39-.07,.53-.22,.14-.14,.22-.32,.22-.53s-.07-.39-.22-.53c-.14-.14-.32-.22-.53-.22H5c-.21,0-.39,.07-.53,.22-.14,.14-.22,.32-.22,.53s.07,.39,.22,.53,.32,.22,.53,.22h6Zm-1.5,6.75l3.75-3.75h-3c-.21,0-.39,.07-.53,.22s-.22,.32-.22,.53v3Z"/></svg>');
}

body:not(.disable-alternative-checkboxes) input[data-task="p"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="p"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="p"]>p>input:checked {
  --checkbox-color-hover: var(--color-green);
  color: var(--color-green);
  -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><defs><style>.cls-1{fill:none;}</style></defs><path class="cls-1" d="M0,16H16V0H0V16Z"/><g><path d="M3.33,15h-.72c-.33,0-.66-.13-.9-.35-.25-.23-.4-.54-.43-.87L.73,7.11c-.02-.18,0-.37,.07-.54,.06-.18,.16-.34,.28-.47,.13-.14,.28-.25,.45-.32,.17-.07,.35-.11,.54-.11h1.27c.36,0,.69,.14,.94,.39,.25,.25,.39,.59,.39,.94v6.67c0,.35-.14,.7-.39,.94-.25,.25-.59,.39-.94,.39Z"/><path d="M15.4,7.69l-1.79,6.34c-.08,.28-.25,.53-.48,.7-.23,.17-.52,.27-.81,.27H6.67c-.36,0-.69-.14-.94-.39-.25-.25-.39-.59-.39-.94V7.13c0-.53,.32-1.02,.81-1.25,.79-.37,1.38-.82,1.62-1.22,.36-.6,.53-1.78,.57-2.65,0-.06,0-.12,.01-.18,.06-.41,.34-.72,.73-.8,.07-.01,.14-.02,.21-.02,.8,0,1.73,.83,2.12,1.48,.29,.48,.41,1.09,.36,1.84-.03,.55-.18,1.05-.33,1.55l-.04,.12h2.72c.21,0,.41,.05,.6,.14,.18,.09,.34,.23,.47,.39,.12,.17,.21,.35,.24,.55,.04,.2,.03,.41-.03,.61ZM9,1.94h0Z"/></g></svg>');
}

body:not(.disable-alternative-checkboxes) input[data-task="c"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="c"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="c"]>p>input:checked {
  --checkbox-color-hover: var(--color-red);
  color: var(--color-red);
  -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><defs><style>.cls-1{fill:none;}</style></defs><path class="cls-1" d="M0,0H16V16H0V0Z"/><g><path d="M3.33,1h-.72c-.33,0-.66,.13-.9,.35-.25,.23-.4,.54-.43,.87l-.56,6.67c-.02,.18,0,.37,.07,.54,.06,.18,.16,.34,.28,.47,.13,.14,.28,.25,.45,.32,.17,.07,.35,.11,.54,.11h1.27c.36,0,.69-.14,.94-.39,.25-.25,.39-.59,.39-.94V2.33c0-.35-.14-.7-.39-.94-.25-.25-.59-.39-.94-.39Z"/><path d="M15.4,8.31l-1.79-6.34c-.08-.28-.25-.53-.48-.7-.23-.17-.52-.27-.81-.27H6.67c-.36,0-.69,.14-.94,.39-.25,.25-.39,.59-.39,.94v6.54c0,.53,.32,1.02,.81,1.25,.79,.37,1.38,.82,1.62,1.22,.36,.6,.53,1.78,.57,2.65,0,.06,0,.12,.01,.18,.06,.41,.34,.72,.73,.8,.07,.01,.14,.02,.21,.02,.8,0,1.73-.83,2.12-1.48,.29-.48,.41-1.09,.36-1.84-.03-.55-.18-1.05-.33-1.55l-.04-.12h2.72c.21,0,.41-.05,.6-.14,.18-.09,.34-.23,.47-.39,.12-.17,.21-.35,.24-.55,.04-.2,.03-.41-.03-.61Zm-6.4,5.75h0Z"/></g></svg>');
}

body:not(.disable-alternative-checkboxes) input[data-task="“"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="“"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="“"]>p>input:checked,
body:not(.disable-alternative-checkboxes) input[data-task="\""]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="\""]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="\""]>p>input:checked {
  --checkbox-color-hover: var(--color-purple);
  color: var(--color-purple);
  -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><defs><style>.cls-1{fill:none;}</style></defs><rect class="cls-1" width="16" height="16"/><path d="M2.46,4.19c.94-1.01,2.35-1.53,4.21-1.53h.67v1.88l-.54,.11c-.91,.18-1.55,.54-1.89,1.07-.18,.28-.28,.61-.29,.94h2.05c.18,0,.35,.07,.47,.2,.13,.13,.2,.29,.2,.47v4.67c0,.74-.6,1.33-1.33,1.33H2c-.18,0-.35-.07-.47-.2-.13-.12-.2-.29-.2-.47V7.39c0-.07-.13-1.83,1.13-3.19ZM13.33,13.33h-4c-.18,0-.35-.07-.47-.2-.13-.12-.2-.29-.2-.47V7.39c0-.07-.13-1.83,1.13-3.19,.94-1.01,2.35-1.53,4.21-1.53h.67v1.88l-.54,.11c-.91,.18-1.55,.54-1.89,1.07-.18,.28-.28,.61-.29,.94h2.05c.18,0,.35,.07,.47,.2,.12,.13,.2,.29,.2,.47v4.67c0,.74-.6,1.33-1.33,1.33Z"/></svg>');
}

body:not(.disable-alternative-checkboxes) input[data-task="S"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="S"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="S"]>p>input:checked {
  --checkbox-color-hover: var(--color-green);
  color: var(--color-green);
  -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><defs><style>.cls-1{fill:none;}.cls-2{fill-rule:evenodd;}</style></defs><rect class="cls-1" width="16" height="16"/><path d="M6.43,5.42c.15-.1,.35-.2,.57-.27v1.7c-.2-.06-.39-.15-.57-.27-.36-.24-.43-.47-.43-.58s.07-.34,.43-.58Zm2.57,5.43v-1.7c.22,.07,.41,.16,.57,.27,.36,.24,.43,.47,.43,.58s-.07,.34-.43,.58c-.18,.11-.37,.2-.57,.27Z"/><path class="cls-2" d="M8,16c2.12,0,4.16-.84,5.66-2.34,1.5-1.5,2.34-3.54,2.34-5.66s-.84-4.16-2.34-5.66c-1.5-1.5-3.54-2.34-5.66-2.34S3.84,.84,2.34,2.34C.84,3.84,0,5.88,0,8s.84,4.16,2.34,5.66c1.5,1.5,3.54,2.34,5.66,2.34Zm1-13c0-.27-.11-.52-.29-.71-.19-.19-.44-.29-.71-.29s-.52,.11-.71,.29c-.19,.19-.29,.44-.29,.71v.09c-.6,.11-1.17,.33-1.68,.66-.72,.48-1.32,1.25-1.32,2.25s.6,1.76,1.32,2.25c.48,.32,1.05,.55,1.68,.66v1.94c-.39-.13-.68-.32-.84-.5-.08-.1-.19-.19-.31-.25-.12-.06-.25-.1-.38-.11s-.27,0-.4,.05c-.13,.04-.25,.11-.35,.2-.1,.09-.18,.19-.24,.32-.06,.12-.09,.25-.1,.38,0,.13,.01,.27,.06,.39,.05,.13,.12,.24,.21,.34,.56,.65,1.41,1.08,2.35,1.25v.09c0,.27,.11,.52,.29,.71,.19,.19,.44,.29,.71,.29s.52-.11,.71-.29c.19-.19,.29-.44,.29-.71v-.09c.6-.11,1.17-.33,1.68-.66,.72-.48,1.32-1.26,1.32-2.25s-.6-1.77-1.32-2.25c-.51-.33-1.08-.56-1.68-.66v-1.94c.39,.13,.68,.32,.84,.5,.09,.1,.19,.18,.31,.25,.12,.06,.25,.1,.38,.11,.13,.01,.26,0,.39-.05,.13-.04,.24-.11,.34-.19,.1-.09,.18-.19,.24-.31,.06-.12,.09-.25,.1-.38,0-.13,0-.26-.05-.39-.04-.13-.11-.24-.2-.34-.56-.65-1.41-1.08-2.35-1.25v-.09Z"/></svg>');
}

body:not(.disable-alternative-checkboxes) input[data-task="u"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="u"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="u"]>p>input:checked {
  --checkbox-color-hover: var(--color-green);
  color: var(--color-green);
  -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><defs><style>.cls-1{fill:none;}.cls-2{fill-rule:evenodd;}</style></defs><rect class="cls-1" width="16" height="16"/><path class="cls-2" d="M10,5c-.27,0-.52-.11-.71-.29-.19-.19-.29-.44-.29-.71s.11-.52,.29-.71c.19-.19,.44-.29,.71-.29h5c.27,0,.52,.11,.71,.29,.19,.19,.29,.44,.29,.71v5c0,.27-.11,.52-.29,.71-.19,.19-.44,.29-.71,.29s-.52-.11-.71-.29c-.19-.19-.29-.44-.29-.71v-2.59l-4.29,4.29c-.19,.19-.44,.29-.71,.29s-.52-.11-.71-.29l-2.29-2.29L1.71,12.71c-.19,.18-.44,.28-.7,.28-.26,0-.51-.11-.7-.29S.01,12.26,.01,12c0-.26,.1-.51,.28-.7L5.29,6.29c.19-.19,.44-.29,.71-.29s.52,.11,.71,.29l2.29,2.29,3.59-3.59h-2.59Z"/></svg>');
}

body:not(.disable-alternative-checkboxes) input[data-task="d"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="d"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="d"]>p>input:checked {
  --checkbox-color-hover: var(--color-red);
  color: var(--color-red);
  -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><defs><style>.cls-1{fill:none;}.cls-2{fill-rule:evenodd;}</style></defs><rect class="cls-1" width="16" height="16"/><path class="cls-2" d="M10,11c-.27,0-.52,.11-.71,.29-.19,.19-.29,.44-.29,.71s.11,.52,.29,.71c.19,.19,.44,.29,.71,.29h5c.27,0,.52-.11,.71-.29,.19-.19,.29-.44,.29-.71V7c0-.27-.11-.52-.29-.71-.19-.19-.44-.29-.71-.29s-.52,.11-.71,.29c-.19,.19-.29,.44-.29,.71v2.59l-4.29-4.29c-.19-.19-.44-.29-.71-.29s-.52,.11-.71,.29l-2.29,2.29L1.71,3.29c-.19-.18-.44-.28-.7-.28-.26,0-.51,.11-.7,.29S.01,3.74,.01,4c0,.26,.1,.51,.28,.7l5,5c.19,.19,.44,.29,.71,.29s.52-.11,.71-.29l2.29-2.29,3.59,3.59h-2.59Z"/></svg>');
}

body:not(.disable-alternative-checkboxes) input[data-task=">"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task=">"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task=">"]>p>input:checked {
  --checkbox-color-hover: var(--color-pink);
  color: var(--color-pink);
  -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><defs><style>.cls-1{fill:none;}</style></defs><rect class="cls-1" width="16" height="16"/><path d="M1.72,1.05c-.08-.04-.18-.06-.27-.05-.09,0-.18,.04-.26,.1-.07,.06-.13,.13-.16,.22-.03,.09-.04,.18-.02,.27l1.4,4.85c.03,.09,.08,.17,.15,.23,.07,.06,.16,.1,.25,.12l5.69,.95c.27,.05,.27,.44,0,.49l-5.69,.95c-.09,.02-.18,.06-.25,.12s-.12,.14-.15,.23l-1.4,4.85c-.02,.09-.01,.19,.02,.27,.03,.09,.09,.16,.16,.22,.07,.06,.16,.09,.26,.1,.09,0,.19,0,.27-.05l13-6.5c.08-.04,.15-.11,.2-.18,.05-.08,.07-.17,.07-.26s-.03-.18-.07-.26c-.05-.08-.12-.14-.2-.18L1.72,1.05Z"/></svg>');
}

body:not(.disable-alternative-checkboxes) input[data-task="<"]:checked,
body:not(.disable-alternative-checkboxes) li[data-task="<"]>input:checked,
body:not(.disable-alternative-checkboxes) li[data-task="<"]>p>input:checked {
  --checkbox-color-hover: var(--color-blue);
  color: var(--color-blue);
  -webkit-mask-image: url('data:image/svg+xml;utf8,<?xml version="1.0" encoding="UTF-8"?><svg id="svg0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"><defs><style>.cls-1{fill:none;}</style></defs><rect class="cls-1" width="16" height="16"/><path d="M2.75,15.5H13.25c.83,0,1.5-.67,1.5-1.5V3.5c0-.83-.67-1.5-1.5-1.5h-1.5V.5h-1.5v1.5H5.75V.5h-1.5v1.5h-1.5c-.83,0-1.5,.67-1.5,1.5V14c0,.83,.67,1.5,1.5,1.5Zm0-11.25H13.25v1.5H2.75v-1.5Z"/></svg>');
}


/* style settings */

.style-settings-container .themed-color-wrapper {
  display: flex;
  gap: var(--size-4-2);
}

.style-settings-container .themed-color-wrapper>div+div {
  margin-top: 0;
}

.style-settings-container {
  margin-left: var(--size-4-1);
  padding-left: var(--size-4-8);
  border-left: 1px solid var(--divider-color);
}

/* printing */

@media print {
  .theme-light {
    --blockquote-background-color: rgba(var(--bg_dark_x), 0.25);
    --code-background: rgba(var(--bg_highlight_dark_x), 0.25);
    --table-header-background: rgba(var(--bg_dark2_x), 0.25);
  }
}


/* @settings

name: Appearance 
id: Appearance 
settings: 
  -
    id: colors
    title: Colors
    type: heading 
    level: 1
    collapsed: true
  -
    id: accent
    title: Accent Color
    type: variable-themed-color
    format: hsl-split
    opacity: false
    default-light: '#0F87CC'
    default-dark: '#80D0FF'
  - 
    id: Extended-colors
    title: Extended Colors
    type: heading
    level: 2
    collapsed: true
  - 
    id: red
    title: Red 
    type: variable-themed-color 
    format: rgb 
    opacity: false 
    default-light: 'rgb(115, 42, 56)' 
    default-dark: 'rgb(219, 75, 75)' 
  - 
    id: red1 
    title: Red1 
    type: variable-themed-color 
    format: rgb 
    opacity: false 
    default-light: 'rgb(140, 67, 81)' 
    default-dark: 'rgb(255, 117, 127)' 
  - 
    id: green 
    title: Green 
    type: variable-themed-color 
    format: rgb 
    opacity: false 
    default-light: 'rgb(51, 99, 92)' 
    default-dark: 'rgb(158, 206, 106)' 
  -   
    id: cyan 
    title: Cyan 
    type: variable-themed-color 
    format: rgb 
    opacity: false 
    default-light: 'rgb(15, 75, 110)' 
    default-dark: 'rgb(125, 207, 255)' 
  - 
    id: blue 
    title: Blue 
    type: variable-themed-color
    format: rgb
    opacity: false
    default-light: 'rgb(52, 84, 138)'
    default-dark: 'rgb(122, 162, 247)'
  - 
    id: yellow 
    title: Yellow 
    type: variable-themed-color 
    format: rgb 
    opacity: false 
    default-light: 'rgb(143, 94, 21)' 
    default-dark: 'rgb(224, 175, 104)'
  - 
    id: orange 
    title: Orange 
    type: variable-themed-color 
    format: rgb 
    opacity: false 
    default-light: 'rgb(150, 80, 39)' 
    default-dark: 'rgb(255, 158, 100)' 
  - 
    id: magenta
    title: Magenta
    type: variable-themed-color 
    format: rgb 
    opacity: false 
    default-light: 'rgb(90, 74, 120)' 
    default-dark: 'rgb(187, 154, 247)' 
  - 
    id: pink 
    title: Pink 
    type: variable-themed-color 
    format: rgb 
    opacity: false 
    default-light: 'rgb(109, 74, 120)'
    default-dark: 'rgb(217, 0, 105)'
  - 
    id: Background-colors 
    title: Background Colors 
    type: heading 
    level: 1 
    collapsed: true 
  - 
    id: bg 
    title: Background Primary 
    type: variable-themed-color 
    format: rgb 
    opacity: false 
    default-light: 'rgb(213, 214, 219)' 
    default-dark: 'rgb(26, 27, 38)' 
  - 
    id: bg_dark 
    title: Background Dark 
    type: variable-themed-color 
    format: rgb 
    opacity: false 
    default-light: 'rgb(203, 204, 209)' 
    default-dark: 'rgb(22, 22, 30)' 
  - 
    id: bg_highlight 
    title: Background Highlight 
    type: variable-themed-color 
    format: rgb 
    opacity: false 
    default-light: 'rgb(220, 222, 226)' 
    default-dark: 'rgb(41, 46, 66)' 
  -  
    id: bg_highlight_dark 
    title: Background Highlight Dark 
    description: 'Used for code bg'
    type: variable-themed-color 
    format: rgb 
    opacity: false 
    default-light: 'rgb(195, 197, 201)' 
    default-dark: 'rgb(36, 40, 59)' 
  - 
    id: bg_dark2 
    title: Background Dark2 
    type: variable-themed-color 
    format: rgb 
    opacity: false 
    default-light: 'rgb(188, 189, 194)' 
    default-dark: 'rgb(15, 15, 20)' 
  - 
    id: foreground-header 
    title: Foreground  
    type: heading 
    level: 2
    collapsed: true 
  - 
    id: text-color-heading 
    title: Text / Icon Color 
    type: heading 
    level: 3 
    collapsed: true 
  - 
    id: text-normal 
    title: Text Normal 
    type: variable-themed-color 
    format: hex
    opacity: false  
    default-light: '#'
    default-dark: '#'
  - 
    id: text-muted 
    title: Text Muted 
    type: variable-themed-color 
    format: hex
    opacity: false  
    default-light: '#'
    default-dark: '#'
  - 
    id: text-faint 
    title: Text Faint 
    type: variable-themed-color 
    format: hex
    opacity: false  
    default-light: '#'
    default-dark: '#'
  -   
    id: text-accent 
    title: Text Accent 
    type: variable-themed-color 
    format: hex 
    opacity: false 
    default-light: '#' 
    default-dark: '#' 
  - 
    id: text-accent-hover 
    title: Text Accent Hover 
    type: variable-themed-color 
    format: hex 
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: text-on-accent 
    title: Text on Accent 
    type: variable-themed-color 
    format: hex 
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: text-on-accent-inverted 
    title: Text on Accent Inverted 
    type: variable-themed-color 
    format: hex 
    opacity: false
    default-light: '#'
    default-dark: '#'
  - 
    id: text-error 
    title: Text Error 
    type: variable-themed-color 
    format: hex 
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: text-warning 
    title: Text Warning 
    type: variable-themed-color 
    format: hex 
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: text-success 
    title: Text Success 
    type: variable-themed-color 
    format: hex 
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: interactive-heading 
    title: Interactive color 
    type: heading 
    level: 3 
    collapsed: true 
  - 
    id: interactive-normal 
    title: Interactive Normal 
    type: variable-themed-color 
    format: hex 
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: interactive-hover 
    title: Interactive Hover 
    type: variable-themed-color 
    format: hex 
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: interactive-accent
    title: Interactive Accent 
    type: variable-themed-color 
    format: hex 
    opacity: false 
    default-light: '#' 
    default-dark: '#' 
  - 
    id: interactive-accent-hover 
    title: Interactive Accent Hover 
    type: variable-themed-color 
    format: hex 
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: interactive-accent-hsl
    title: Interactive Accent HSL 
    type: variable-themed-color 
    format: hex
    opacity: false 
    default-light: '#'
    default-dark: '#'
  -
*/

/* @settings

name: Editor
id: Editor
settings: 
  - 
    id: headings 
    title: Tabs 
    type: heading 
    level: 1 
    collapsed: true 
  - 
    id: tab-text-color-focused-active 
    title: Tab Focus Color 
    type: variable-themed-color 
    format: rgb 
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: tab-text-color-focused-active-current
    title: Current Tab Focus Color 
    type: variable-themed-color 
    format: rgb 
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: headings 
    title: Headings
    type: heading 
    level: 1 
    collapsed: true 
  - 
    id: level-1-heading
    title: Level 1
    type: heading 
    level: 2 
    collapsed: true 
  - 
		id: h1-divider-on
		title: disable H1 divider
		type: class-toggle
		default: false
  - 
    id: h1-font
    title: H1 Font 
    type: variable-text
    default: ''
  - 
    id: h1-size
    title: H1 Font Size 
    description: Any CSS font-size value
    type: variable-text 
    default: ''
  - 
    id: h1-weight
    title: H1 Font Weight 
    description: Value from 100 - 900
    type: variable-text 
    default: 700
	-  
    id: h1-color 
    title: H1 Color 
    type: variable-themed-color 
    format: rgb 
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: level-2-heading
    title: Level 2
    type: heading 
    level: 2 
    collapsed: true 
  - 
		id: h2-divider-on
		title: disable H2 divider
		type: class-toggle
		default: false
  - 
    id: h2-font
    title: H2 Font 
    type: variable-text
    default: ''
  - 
    id: h2-size
    title: H2 Font Size 
    description: Any CSS font-size value
    type: variable-text 
    default: ''
  - 
    id: h2-weight
    title: H2 Font Weight 
    description: Value from 100 - 900
    type: variable-text 
    default: 600
	-  
    id: h2-color 
    title: H2 Color 
    type: variable-themed-color 
    format: rgb 
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: level-3-heading
    title: Level 3
    type: heading 
    level: 2 
    collapsed: true 
  - 
		id: h3-divider-on
		title: disable H3 divider
		type: class-toggle
		default: false
  - 
    id: h3-font
    title: H3 Font 
    type: variable-text
    default: ''
  - 
    id: h3-size
    title: H3 Font Size 
    description: Any CSS font-size value
    type: variable-text 
    default: ''
  - 
    id: h3-weight
    title: H3 Font Weight 
    description: Value from 100 - 900
    type: variable-text 
    default: 600
	-  
    id: h3-color 
    title: H3 Color 
    type: variable-themed-color 
    format: rgb 
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: level-4-heading
    title: Level 4
    type: heading 
    level: 2 
    collapsed: true 
  - 
		id: h4-divider-on
		title: disable H4 divider
		type: class-toggle
		default: false
  - 
    id: h4-font
    title: H4 Font 
    type: variable-text
    default: ''
  - 
    id: h4-size
    title: H4 Font Size 
    description: Any CSS font-size value
    type: variable-text 
    default: ''
  - 
    id: h4-weight
    title: H4 Font Weight 
    description: Value from 100 - 900
    type: variable-text 
    default: 600
	-  
    id: h4-color 
    title: H4 Color 
    type: variable-themed-color 
    format: rgb 
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: level-5-heading
    title: Level 5
    type: heading 
    level: 2 
    collapsed: true 
  - 
		id: h5-divider-on
		title: disable H5 divider
		type: class-toggle
		default: false
  - 
    id: h5-font
    title: H5 Font 
    type: variable-text
    default: ''
  - 
    id: h5-size
    title: H5 Font Size 
    description: Any CSS font-size value
    type: variable-text 
    default: ''
  - 
    id: h5-weight
    title: H5 Font Weight 
    description: Value from 100 - 900
    type: variable-text 
    default: 600
	-  
    id: h5-color 
    title: H5 Color 
    type: variable-themed-color 
    format: rgb 
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: level-6-heading
    title: Level 6
    type: heading 
    level: 2 
    collapsed: true 
  - 
		id: h6-divider-on
		title: disable H6 divider
		type: class-toggle
		default: false
  - 
    id: h6-font
    title: H6 Font 
    type: variable-text
    default: ''
  - 
    id: h6-size
    title: H6 Font Size 
    description: Any CSS font-size value
    type: variable-text 
    default: ''
  - 
    id: h6-weight
    title: H6 Font Weight 
    description: Value from 100 - 900
    type: variable-text 
    default: 600
	-  
    id: h6-color 
    title: H6 Color 
    type: variable-themed-color 
    format: rgb 
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: text-heading 
    title: Text 
    type: heading  
    level: 1 
    collapsed: true 
  - 
    id: bold-color 
    title: Bold Color 
    type: variable-themed-color 
    format: rgb 
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: italic-color 
    title: Italic Color 
    type: variable-themed-color 
    format: rgb 
    opacity: false 
    default-light: '#'
    default-dark: '#'
  -
    id: link-heading 
    title: Links 
    type: heading  
    level: 1 
    collapsed: true 
  - 
    id: link-color 
    title: Link Color 
    type: variable-themed-color 
    format: rgb 
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: link-color-hover 
    title: Link Hover Color 
    type: variable-themed-color 
    format: rgb 
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: link-decoration
		title: Link decoration
    description: Any CSS text-decoration-line value
		type: variable-text
		default: underline
  - 
    id: link-decoration-hover 
    title: Link Hover decoration
    description: Any CSS text-decoration-line value
    type: variable-text   
    default: underline
  - 
    id: link-decoration-thickness 
    title: Link decoration thickness 
    description: Any CSS text-decoration-thickness value
    type: variable-text 
    default: auto
  - 
    id: external-link-heading 
    title: External Link 
    type: heading  
    level: 3
    collapsed: true
  - 
    id: link-external-color 
    title: External Link Color 
    type: variable-themed-color 
    format: rgb 
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: link-external-color-hover 
    title: External Link Hover Color 
    type: variable-themed-color 
    format: rgb 
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: link-external-decoration
    title: External Link decoration
    description: Any CSS text-decoration-line value
    type: variable-text
    default: underline
  - 
    id: link-external-decoration-hover 
    title: External Link Hover decoration
    description: Any CSS text-decoration-line value
    type: variable-text   
    default: underline 
  - 
    id: link-external-filter 
    title: External Link Filter 
    description: Any CSS filter value
    type: variable-text 
    default: invert(35%) sepia(28%) saturate(681%) hue-rotate(192deg) brightness(94%) contrast(85%)
  -
    id: unresolved-link-heading 
    title: Unresolved Link
    type: heading
    level: 3
    collapsed: true
  - 
    id: link-unresolved-color 
    title: Unresolved Link Color 
    type: variable-themed-color 
    format: rgb 
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
		id: link-unresolved-opacity
		title: Unresolved link opacity
		type: variable-number-slider
		default: 0.7
		min: 0.25
		max: 1
		step: 0.05
  - 
    id: link-unresolved-filter 
    title: Unresolved Link Filter 
    description: Any CSS filter value
    type: variable-text 
    default: none
  -
    id: link-unresolved-decoration
    title: Unresolved Link decoration
    description: Any CSS text-decoration-line value
    type: variable-text
    default: solid
  - 
    id: link-unresolved-decoration-color 
    title: Unresolved Link decoration Color 
    type: variable-themed-color 
    format: rgb 
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: lists-heading 
    title: Lists 
    type: heading  
    level: 2 
    collapsed: true 
  - 
    id: list-indent 
    title: List Indent
    type: variable-text
    default: 2.25em
  - 
		id: list-spacing
		title: list spacing
		type: variable-text
		default: 0.075em
  - 
		id: list-bullet-size
		title: list marker size
		type: variable-text
		default: 0.3em
	- 
		id: list-marker-color
		title: list marker color
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
  - 
		id: list-marker-color-hover
		title: list marker color (hover)
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
  - 
		id: list-marker-color-collapsed
		title: list marker color (collapsed)
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
  - 
    id: Tags
    title: Tags
    type: heading
    collapsed: true
    level: 2
  - 
		id: tag-color
		title: Tag text color
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	- 
		id: tag-background
		title: Tag background color
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
	- 
		id: tag-background-hover
		title: Tag background color (hover)
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
  - 
    id: Dates 
    title: Dates 
    type: heading 
    collapsed: true 
    level: 2
  - 
    id: date-color 
		title: Tag text color
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
  - 
		id: date-background-color
		title: Date background color
		type: variable-themed-color
		format: hex
		default-light: '#'
		default-dark: '#'
  - 
    id: table-heading 
    title: Tables 
    type: heading  
    level: 2 
    collapsed: true 
  - 
    id: table-border-width 
    title: Table Border Width 
    type: variable-number-slider
    default: 1
    min: 0
    max: 20
    step: 1
    format: px
  - 
    id: table-border-color 
    title: Table Border Color 
    type: variable-themed-color 
    format: rgb 
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: code-headigeng 
    title: Code 
    type: heading  
    level: 2 
    collapsed: true
  - 
    id: code-background
    title: Code Background
    type: variable-themed-color
    opacity: true
    format: hex
    default-light: '#'
    default-dark: '#'
  - 
		id: code-normal
		title: Code Normal
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
  - 
		id: code-comment
		title: Code Comment
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
  - 
    id: code-function
		title: Code Function
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
  - 
    id: code-important
		title: Code Important
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
  - 
    id: code-keyword
		title: Code Keyword
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
  - 
    id: code-operator
		title: Code Operator
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
  - 
    id: code-property
		title: Code Property
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
  - 
    id: code-punctuation
		title: Code Punctuation
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
  - 
    id: code-string
		title: Code String
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
  - 
    id: code-tag
		title: Code Tag
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
  - 
    id: code-value
		title: Code Value
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
  - 
    id: blockquote-heading 
    title: Blockquote 
    type: heading  
    level: 2 
    collapsed: true 
  - 
    id: blockquote-border-thickness
    title: Blockquote Border Thickness
    type: variable-number-slider
    default: 3
    min: 0 
    max: 20 
    step: 1 
    format: px
  - 
    id: blockquote-border-color
    title: Blockquote Border Color
    type: variable-themed-color
    format: hex
    default-light: '#'
    default-dark: '#'
  - 
    id: blockquote-color 
    title: Blockquote Color 
    type: variable-themed-color 
    format: hex
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: blockquote-background-color
    title: Blockquote Background Color
    type: variable-themed-color
    format: hex
    default-light: '#'
    default-dark: '#'
  - 
    id: checkbox-heading 
    title: Checkboxes
    type: heading
    level: 2
    collapsed: true
  - 
    id: disable-alternative-checkboxes
    title: Disable Alternative Checkboxes
    description: Enable this if you are using your own implementation via a CSS Snippet.
    default: false
    type: class-toggle
  - 
    id: checkbox-radius 
    title: Checkbox Radius 
    description: Any CSS border-radius value
    type: variable-text
    default: ''
  - 
    id: checkbox-color 
    title: Checkbox Color 
    type: variable-themed-color  
    format: hex 
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: checkbox-color-hover 
    title: Checkbox Hover Color 
    type: variable-themed-color 
    format: hex 
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: checkbox-marker-color 
    title: Checkbox Marker Color 
    type: variable-themed-color 
    format: hex 
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: checkbox-border-color 
    title: Checkbox Border Color 
    type: variable-themed-color 
    format: hex 
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: checkbox-border-color-hover 
    title: Checkbox Border Hover Color 
    type: variable-themed-color 
    format: hex 
    opacity: false 
    default-light: '#'
    default-dark: '#'
  -
    id: callout-heading 
    title: Callouts 
    type: heading  
    level: 2 
    collapsed: true
  - 
    id: callout-style-select
    title: Callout Style
    type: class-select
    allowEmpty: false
    default: callout-style-customize
    options:
        -   label: Customize
            value: callout-style-customize
        -   label: style 1
            value: callout-style-1
        -   label: style 2
            value: callout-style-2
        -   label: style 3
            value: callout-style-3
        -   label: style 4
            value: callout-style-4
  - 
		id: callout-border-width
		title: Callout Border Width
    type: variable-text
    description: Any CSS border-width value
    default: 0px
  - 
    id: callout-border-opacity
    title: Callout Border Opacity
    type: variable-number-slider
    default: 0.25
    min: 0
    max: 1
    step: 0.05
  - 
		id: callout-padding
		title: Callout Padding
    description: Accepts any CSS padding value
		type: variable-text
		default: ''
  - 
    id: callout-radius
    title: Callout Radius
    description: Input your border-radius value here
    type: variable-text
    default: ''
	- 
		id: callout-title-color
		title: Callout Title Color
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
  - 
		id: callout-title-padding
		title: Callout Title Padding
    description: Any CSS font-size value
		type: variable-text
		default: ''
  - 
		id: callout-title-size
		title: Callout Title Size
    description: Any CSS font-size value
		type: variable-text
		default: ''
  - 
		id: callout-content-padding
		title: Callout Content Padding
    description: Any CSS font-size value
		type: variable-text
		default: ''
  - 
		id: callout-content-background
		title: Callout Content Background
		type: variable-themed-color
		opacity: true
		format: hex
		default-light: '#'
		default-dark: '#'
  - 
    id: callout-content-radius
    title: Callout Content Radius
    description: Input your border-radius value here
    type: variable-text
    default: ''
  - 
    id: callout-color-heading 
    title: Callout Colors 
    type: heading  
    level: 3 
    collapsed: true 
  -
    id: callout-default 
    title: Default Callout 
    type: variable-themed-color 
    format: hex
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: callout-info 
    title: Info Callout 
    type: variable-themed-color 
    format: hex
    opacity: false 
    default-light: '#'
    default-dark: '#'
  -  
    id: callout-summary 
    title: Summary Callout  
    type: variable-themed-color 
    format: hex
    opacity: false
    default-light: '#'
    default-dark: '#'
  - 
    id: callout-tip 
    title: Tip Callout 
    type: variable-themed-color 
    format: hex
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: callout-todo
    title: Todo Callout 
    type: variable-themed-color 
    format: hex
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: callout-bug 
    title: Bug Callout 
    type: variable-themed-color 
    format: hex
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: callout-error 
    title: Error Callout 
    type: variable-themed-color 
    format: hex
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: callout-fail 
    title: Fail Callout 
    type: variable-themed-color 
    format: hex
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: callout-example
    title: Example Callout 
    type: variable-themed-color 
    format: hex
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: callout-important
    title: Important Callout 
    type: variable-themed-color 
    format: hex
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: callout-success 
    title: Success Callout 
    type: variable-themed-color 
    format: hex
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: callout-question
    title: Question Callout 
    type: variable-themed-color 
    format: hex
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: callout-warning 
    title: Warning Callout 
    type: variable-themed-color 
    format: hex
    opacity: false 
    default-light: '#'
    default-dark: '#'
  - 
    id: callout-quote
    title: Quote Callout 
    type: variable-themed-color 
    format: hex
    opacity: false 
    default-light: '#'
    default-dark: '#'
  -
    id: embed-heading 
    title: Embeds 
    type: heading  
    level: 2 
    collapsed: true 
  - 
    id: embed-background
    title: Embed Background
    type: variable-themed-color
    opacity: true
    format: hex
    default-light: '#'
    default-dark: '#'
  - 
    id: embed-padding
    title: Embed Padding
    description: Any CSS padding value
    type: variable-text
    default: ''
  -         
    id: embed-border-radius
    title: Embed Border Radius
    description: Any CSS border-radius value
    type: variable-text
    default: ''
  - 
    id: embed-font-style
    title: Embed Font Style
    description: Any CSS font-style value
    type: variable-text
    default: ''
  - 
    id: image-heading 
    title: Images
    type: heading 
    level: 2
    collapsed: true 
  - 
    id: image-radius 
    title: Image Radius 
    description: Any CSS border-radius value 
    type: variable-text 
    default: ''
  -
*/
